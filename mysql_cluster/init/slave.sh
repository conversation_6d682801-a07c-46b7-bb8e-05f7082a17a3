#!/bin/bash

# 等待主服务器完全启动
echo "Waiting for master to become available..."
while ! mysqladmin ping -h"mysql_master" -P"3306" --silent; do
    sleep 1
done
echo "Master is available."

# 配置并启动复制
mysql -uroot -p"$MYSQL_ROOT_PASSWORD" -e " \
  CHANGE MASTER TO \
    MASTER_HOST='mysql_master', \
    MASTER_USER='replicator', \
    MASTER_PASSWORD='replication_password', \
    MASTER_AUTO_POSITION=1; \
  START SLAVE; \
  SHOW SLAVE STATUS\G; \
"
echo "Replication configuration complete." 