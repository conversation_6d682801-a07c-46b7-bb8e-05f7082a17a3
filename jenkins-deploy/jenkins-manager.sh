#!/bin/bash

# Jenkins 管理脚本
# 用于管理Jenkins Docker容器的常用操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_blue() {
    echo -e "${BLUE}[EXEC]${NC} $1"
}

# 显示Jenkins状态
status() {
    log_info "=== Jenkins 服务状态 ==="
    docker-compose ps
    echo ""
    
    if docker ps | grep -q jenkins; then
        log_info "Jenkins 正在运行"
        echo "访问地址: http://$(hostname -I | awk '{print $1}'):8080"
    else
        log_warn "Jenkins 未运行"
    fi
}

# 启动Jenkins
start() {
    log_info "启动Jenkins服务..."
    docker-compose up -d
    log_info "Jenkins 启动完成"
}

# 停止Jenkins
stop() {
    log_info "停止Jenkins服务..."
    docker-compose down
    log_info "Jenkins 停止完成"
}

# 重启Jenkins
restart() {
    log_info "重启Jenkins服务..."
    docker-compose restart
    log_info "Jenkins 重启完成"
}

# 查看日志
logs() {
    log_info "显示Jenkins日志..."
    docker-compose logs -f jenkins
}

# 进入Jenkins容器
shell() {
    log_info "进入Jenkins容器..."
    docker exec -it jenkins bash
}

# 获取初始管理员密码
password() {
    log_info "获取Jenkins初始管理员密码..."
    if docker ps | grep -q jenkins; then
        docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword
    else
        log_error "Jenkins 容器未运行"
    fi
}

# 备份Jenkins数据
backup() {
    log_info "备份Jenkins数据..."
    BACKUP_DIR="./jenkins-backup/backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    if docker ps | grep -q jenkins; then
        docker exec jenkins tar czf /backup/jenkins-backup-$(date +%Y%m%d-%H%M%S).tar.gz -C /var/jenkins_home .
        log_info "备份完成: $BACKUP_DIR"
    else
        log_error "Jenkins 容器未运行"
    fi
}

# 恢复Jenkins数据
restore() {
    if [[ -z "$1" ]]; then
        log_error "请指定备份文件路径"
        echo "用法: $0 restore <backup-file>"
        exit 1
    fi
    
    log_warn "恢复操作将覆盖现有数据，确认继续吗？(y/N)"
    read -r confirm
    if [[ $confirm != "y" && $confirm != "Y" ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "恢复Jenkins数据..."
    docker-compose down
    docker exec jenkins tar xzf /backup/$1 -C /var/jenkins_home
    docker-compose up -d
    log_info "恢复完成"
}

# 更新Jenkins
update() {
    log_info "更新Jenkins镜像..."
    docker-compose down
    docker-compose pull
    docker-compose up -d
    log_info "Jenkins 更新完成"
}

# 清理未使用的Docker资源
cleanup() {
    log_info "清理未使用的Docker资源..."
    docker system prune -f
    docker volume prune -f
    log_info "清理完成"
}

# 显示系统信息
info() {
    log_info "=== 系统信息 ==="
    echo "Docker 版本: $(docker --version)"
    echo "Docker Compose 版本: $(docker-compose --version)"
    echo "系统信息: $(uname -a)"
    echo "内存使用: $(free -h | grep Mem)"
    echo "磁盘使用: $(df -h | grep -E '/$')"
    echo ""
    
    log_info "=== Jenkins 容器信息 ==="
    if docker ps | grep -q jenkins; then
        docker stats jenkins --no-stream
    else
        log_warn "Jenkins 容器未运行"
    fi
}

# 显示帮助信息
usage() {
    echo "Jenkins 管理脚本"
    echo ""
    echo "用法: $0 <command> [options]"
    echo ""
    echo "命令:"
    echo "  status          显示Jenkins服务状态"
    echo "  start           启动Jenkins服务"
    echo "  stop            停止Jenkins服务"
    echo "  restart         重启Jenkins服务"
    echo "  logs            查看Jenkins日志"
    echo "  shell           进入Jenkins容器"
    echo "  password        获取初始管理员密码"
    echo "  backup          备份Jenkins数据"
    echo "  restore <file>  恢复Jenkins数据"
    echo "  update          更新Jenkins镜像"
    echo "  cleanup         清理未使用的Docker资源"
    echo "  info            显示系统和容器信息"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status"
    echo "  $0 logs"
    echo "  $0 backup"
    echo "  $0 restore jenkins-backup-20231201-120000.tar.gz"
}

# 主函数
main() {
    case "$1" in
        status)
            status
            ;;
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        logs)
            logs
            ;;
        shell)
            shell
            ;;
        password)
            password
            ;;
        backup)
            backup
            ;;
        restore)
            restore "$2"
            ;;
        update)
            update
            ;;
        cleanup)
            cleanup
            ;;
        info)
            info
            ;;
        help|--help|-h)
            usage
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            usage
            exit 1
            ;;
    esac
}

# 检查参数
if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

# 执行主函数
main "$@"
