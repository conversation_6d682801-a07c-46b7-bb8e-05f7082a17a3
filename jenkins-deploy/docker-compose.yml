version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts-jdk11
    container_name: jenkins-master
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "8080:8080"      # Jenkins Web UI 端口
      - "50000:50000"    # Jenkins 代理连接端口
    
    # 环境变量配置
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Xmx2048m -Xms1024m
      - JENKINS_OPTS=--httpPort=8080
      - TZ=Asia/Shanghai  # 设置时区
    
    # 数据卷挂载 - 持久化Jenkins数据
    volumes:
      - jenkins_home:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock  # 允许Jenkins使用Docker
      - /usr/bin/docker:/usr/bin/docker:ro          # Docker二进制文件
      - ./jenkins-backup:/backup                    # 备份目录
    
    # 网络配置
    networks:
      - jenkins-network
    
    # 用户配置 - 解决权限问题
    user: root
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/login || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Jenkins 代理节点（可选）
  jenkins-agent:
    image: jenkins/inbound-agent:latest-jdk11
    container_name: jenkins-agent-1
    restart: unless-stopped
    
    environment:
      - JENKINS_URL=http://jenkins:8080
      - JENKINS_AGENT_NAME=docker-agent-1
      - JENKINS_AGENT_WORKDIR=/home/<USER>/agent
      # 注意：JENKINS_SECRET 需要在Jenkins启动后从Web界面获取
      # - JENKINS_SECRET=your-agent-secret-here
    
    volumes:
      - jenkins_agent_workspace:/home/<USER>/agent
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker:ro
    
    networks:
      - jenkins-network
    
    depends_on:
      jenkins:
        condition: service_healthy

  # Nginx 反向代理（可选 - 用于域名访问和SSL）
  nginx:
    image: nginx:alpine
    container_name: jenkins-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    
    networks:
      - jenkins-network
    
    depends_on:
      - jenkins

# 网络配置
networks:
  jenkins-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  jenkins_home:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/jenkins/jenkins_home  # Rocky Linux上的持久化路径
  
  jenkins_agent_workspace:
    driver: local
