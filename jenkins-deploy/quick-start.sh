#!/bin/bash

# Jenkins 快速启动脚本
# 一键检查环境、部署和启动Jenkins

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    Jenkins Docker Compose 快速启动脚本"
    echo "    适用于 Rocky Linux / CentOS / RHEL"
    echo "=================================================="
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_system() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ -f /etc/redhat-release ]]; then
        OS_VERSION=$(cat /etc/redhat-release)
        log_info "检测到系统: $OS_VERSION"
    else
        log_warn "未检测到Red Hat系列系统，脚本可能需要调整"
    fi
    
    # 检查内存
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [[ $TOTAL_MEM -lt 2 ]]; then
        log_error "系统内存不足2GB，建议至少4GB内存"
        exit 1
    else
        log_info "系统内存: ${TOTAL_MEM}GB ✓"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $DISK_SPACE -lt 10 ]]; then
        log_error "磁盘空间不足10GB，建议至少20GB可用空间"
        exit 1
    else
        log_info "可用磁盘空间: ${DISK_SPACE}GB ✓"
    fi
}

# 安装Docker
install_docker() {
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装: $(docker --version)"
        return 0
    fi
    
    log_step "安装Docker..."
    
    # 更新系统
    sudo dnf update -y
    
    # 安装Docker
    sudo dnf install -y docker
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 添加用户到docker组
    sudo usermod -aG docker $USER
    
    log_info "Docker 安装完成"
    log_warn "请重新登录或执行 'newgrp docker' 使组权限生效"
}

# 安装Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装: $(docker-compose --version)"
        return 0
    fi
    
    log_step "安装Docker Compose..."
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_info "Docker Compose 安装完成: $(docker-compose --version)"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v firewall-cmd &> /dev/null; then
        # 检查防火墙状态
        if sudo firewall-cmd --state &> /dev/null; then
            sudo firewall-cmd --permanent --add-port=8080/tcp
            sudo firewall-cmd --permanent --add-port=50000/tcp
            sudo firewall-cmd --permanent --add-port=80/tcp
            sudo firewall-cmd --permanent --add-port=443/tcp
            sudo firewall-cmd --reload
            log_info "防火墙规则配置完成"
        else
            log_warn "防火墙未运行，跳过配置"
        fi
    else
        log_warn "未找到firewall-cmd，请手动配置防火墙"
    fi
}

# 选择部署模式
choose_deployment_mode() {
    echo ""
    log_step "选择部署模式:"
    echo "1) 简化模式 (推荐新手) - 只包含Jenkins核心功能"
    echo "2) 完整模式 - 包含Jenkins + 代理 + Nginx反向代理"
    echo "3) 自定义模式 - 手动选择组件"
    echo ""
    
    while true; do
        read -p "请选择部署模式 [1-3]: " choice
        case $choice in
            1)
                DEPLOYMENT_MODE="simple"
                log_info "选择了简化模式"
                break
                ;;
            2)
                DEPLOYMENT_MODE="full"
                log_info "选择了完整模式"
                break
                ;;
            3)
                DEPLOYMENT_MODE="custom"
                log_info "选择了自定义模式"
                break
                ;;
            *)
                log_error "无效选择，请输入1-3"
                ;;
        esac
    done
}

# 部署Jenkins
deploy_jenkins() {
    log_step "部署Jenkins..."
    
    # 给脚本添加执行权限
    chmod +x deploy-jenkins.sh jenkins-manager.sh
    
    case $DEPLOYMENT_MODE in
        "simple")
            ./deploy-jenkins.sh simple
            ;;
        "full")
            ./deploy-jenkins.sh
            ;;
        "custom")
            log_info "自定义模式，请手动执行部署命令"
            echo "简化部署: ./deploy-jenkins.sh simple"
            echo "完整部署: ./deploy-jenkins.sh"
            return 0
            ;;
    esac
}

# 显示访问信息
show_access_info() {
    log_step "获取访问信息..."
    
    # 等待Jenkins启动
    echo "等待Jenkins启动..."
    sleep 30
    
    # 获取服务器IP
    SERVER_IP=$(hostname -I | awk '{print $1}')
    
    echo ""
    echo -e "${GREEN}=================================================="
    echo "           Jenkins 部署完成！"
    echo "==================================================${NC}"
    echo ""
    echo -e "${BLUE}访问信息:${NC}"
    echo "  Web界面: http://$SERVER_IP:8080"
    echo "  本地访问: http://localhost:8080"
    echo ""
    
    # 获取初始密码
    echo -e "${BLUE}初始管理员密码:${NC}"
    if docker ps | grep -q jenkins; then
        echo "正在获取初始密码..."
        sleep 10
        if docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword 2>/dev/null; then
            echo ""
        else
            echo "密码获取失败，请稍后执行: ./jenkins-manager.sh password"
        fi
    else
        echo "Jenkins容器未运行，请检查部署状态"
    fi
    
    echo ""
    echo -e "${BLUE}常用管理命令:${NC}"
    echo "  查看状态: ./jenkins-manager.sh status"
    echo "  查看日志: ./jenkins-manager.sh logs"
    echo "  获取密码: ./jenkins-manager.sh password"
    echo "  重启服务: ./jenkins-manager.sh restart"
    echo "  停止服务: ./jenkins-manager.sh stop"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "  1. 首次启动可能需要2-3分钟"
    echo "  2. 建议安装推荐的插件"
    echo "  3. 定期备份Jenkins数据"
    echo "  4. 查看完整文档: cat README.md"
    echo ""
}

# 主函数
main() {
    show_banner
    
    # 检查是否在正确的目录
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "请在jenkins-deploy目录中运行此脚本"
        exit 1
    fi
    
    log_info "开始Jenkins快速部署流程..."
    
    check_system
    install_docker
    install_docker_compose
    configure_firewall
    choose_deployment_mode
    deploy_jenkins
    show_access_info
    
    log_info "Jenkins快速启动脚本执行完成！"
}

# 脚本使用说明
usage() {
    echo "Jenkins 快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -s, --simple   直接使用简化模式部署"
    echo "  -f, --full     直接使用完整模式部署"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式选择部署模式"
    echo "  $0 --simple     # 直接使用简化模式"
    echo "  $0 --full       # 直接使用完整模式"
}

# 参数处理
case "$1" in
    -h|--help)
        usage
        exit 0
        ;;
    -s|--simple)
        DEPLOYMENT_MODE="simple"
        ;;
    -f|--full)
        DEPLOYMENT_MODE="full"
        ;;
    "")
        # 无参数，使用交互模式
        ;;
    *)
        log_error "未知参数: $1"
        usage
        exit 1
        ;;
esac

# 执行主函数
main
