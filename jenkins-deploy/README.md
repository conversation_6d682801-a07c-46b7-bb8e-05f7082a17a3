# Jenkins Docker Compose 部署指南

## 📁 项目结构

```
jenkins-deploy/
├── docker-compose.yml              # 完整配置（包含代理和Nginx）
├── docker-compose-simple.yml       # 简化配置（推荐新手）
├── deploy-jenkins.sh               # 自动化部署脚本
├── jenkins-manager.sh              # Jenkins管理脚本
├── nginx/
│   └── nginx.conf                  # Nginx反向代理配置
└── README.md                       # 本文档
```

## 🚀 快速开始

### 1. 下载部署包
```bash
# 将整个jenkins-deploy文件夹复制到您的Rocky Linux服务器
# 进入部署目录
cd jenkins-deploy
```

### 2. 一键部署
```bash
# 给脚本添加执行权限
chmod +x deploy-jenkins.sh jenkins-manager.sh

# 快速部署（简化版，推荐新手）
./deploy-jenkins.sh simple

# 或者完整部署（包含代理和Nginx）
./deploy-jenkins.sh
```

### 3. 访问Jenkins
```bash
# 获取初始密码
./jenkins-manager.sh password

# 访问地址：http://YOUR_SERVER_IP:8080
```

## 📋 详细部署指南

### 1. 环境准备

#### 1.1 安装 Docker
```bash
# 更新系统
sudo dnf update -y

# 安装 Docker
sudo dnf install -y docker

# 启动并启用 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使组权限生效
newgrp docker
```

#### 1.2 安装 Docker Compose
```bash
# 下载 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 2. 部署选项

#### 2.1 快速部署（推荐新手）
```bash
# 使用简化配置部署
./deploy-jenkins.sh simple
```

#### 2.2 完整部署（包含代理和Nginx）
```bash
# 使用完整配置部署
./deploy-jenkins.sh
```

#### 2.3 手动部署
```bash
# 创建必要目录
sudo mkdir -p /opt/jenkins/jenkins_home
sudo chown -R 1000:1000 /opt/jenkins/jenkins_home

# 启动服务（简化版）
docker-compose -f docker-compose-simple.yml up -d

# 或启动完整版
docker-compose up -d
```

### 3. 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=8080/tcp  # Jenkins Web UI
sudo firewall-cmd --permanent --add-port=50000/tcp # Jenkins 代理
sudo firewall-cmd --permanent --add-port=80/tcp    # HTTP
sudo firewall-cmd --permanent --add-port=443/tcp   # HTTPS
sudo firewall-cmd --reload
```

### 4. 访问 Jenkins

#### 4.1 获取访问地址
```bash
# 获取服务器IP
ip addr show | grep inet

# Jenkins访问地址
http://YOUR_SERVER_IP:8080
```

#### 4.2 获取初始管理员密码
```bash
# 方法1：使用管理脚本
./jenkins-manager.sh password

# 方法2：直接从容器获取
docker exec jenkins cat /var/jenkins_home/secrets/initialAdminPassword
```

## 🔧 Jenkins 管理

### 5.1 使用管理脚本
```bash
# 查看服务状态
./jenkins-manager.sh status

# 查看日志
./jenkins-manager.sh logs

# 重启服务
./jenkins-manager.sh restart

# 备份数据
./jenkins-manager.sh backup

# 查看所有可用命令
./jenkins-manager.sh help
```

### 5.2 常用 Docker Compose 命令
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f jenkins

# 查看服务状态
docker-compose ps
```

## ⚙️ 初始配置

### 6.1 首次登录设置
1. 访问 `http://YOUR_SERVER_IP:8080`
2. 输入初始管理员密码
3. 选择"安装推荐的插件"
4. 创建管理员用户
5. 配置Jenkins URL

### 6.2 推荐安装的插件
- **Git Plugin** - Git版本控制支持
- **Pipeline Plugin** - 流水线支持
- **Docker Plugin** - Docker集成
- **Blue Ocean** - 现代化UI界面
- **Chinese Localization Plugin** - 中文本地化

## 🔍 故障排除

### 7.1 常见问题

**问题1：Jenkins 无法启动**
```bash
# 检查日志
docker-compose logs jenkins

# 检查端口占用
sudo netstat -tlnp | grep 8080

# 检查磁盘空间
df -h
```

**问题2：权限问题**
```bash
# 修复Jenkins数据目录权限
sudo chown -R 1000:1000 /opt/jenkins/jenkins_home
sudo chmod -R 755 /opt/jenkins/jenkins_home
```

**问题3：内存不足**
```bash
# 检查内存使用
free -h

# 调整Java堆内存（在docker-compose.yml中）
JAVA_OPTS=-Xmx1024m -Xms512m
```

### 7.2 性能优化

**调整JVM参数**
```yaml
environment:
  - JAVA_OPTS=-Xmx4096m -Xms2048m -XX:+UseG1GC
```

**增加资源限制**
```yaml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
```

## 💾 备份和恢复

### 8.1 自动备份
```bash
# 创建备份
./jenkins-manager.sh backup

# 设置定时备份（crontab）
0 2 * * * /path/to/jenkins-deploy/jenkins-manager.sh backup
```

### 8.2 数据恢复
```bash
# 恢复备份
./jenkins-manager.sh restore backup-file.tar.gz
```

## 🔒 安全建议

1. **更改默认端口**：修改docker-compose.yml中的端口映射
2. **使用HTTPS**：配置SSL证书
3. **设置防火墙**：只开放必要端口
4. **定期备份**：设置自动备份策略
5. **更新镜像**：定期更新Jenkins镜像

## 📊 监控和维护

```bash
# 查看资源使用情况
./jenkins-manager.sh info

# 清理未使用的Docker资源
./jenkins-manager.sh cleanup

# 更新Jenkins
./jenkins-manager.sh update
```

## 📈 资源要求

- **最小配置**：2GB RAM, 2 CPU核心, 10GB磁盘空间
- **推荐配置**：4GB RAM, 4 CPU核心, 50GB磁盘空间
- **生产环境**：8GB RAM, 8 CPU核心, 100GB磁盘空间

## 🆘 技术支持

如果遇到问题，请检查：
1. Docker和Docker Compose是否正确安装
2. 防火墙设置是否正确
3. 系统资源是否充足
4. 日志文件中的错误信息

### 常用诊断命令
```bash
# 检查Docker状态
sudo systemctl status docker

# 检查容器状态
docker ps -a

# 检查网络连接
curl -I http://localhost:8080

# 检查系统资源
./jenkins-manager.sh info
```
