# Jenkins Docker Compose 部署方案

## 📦 项目概述

这是一个完整的Jenkins Docker Compose部署方案，专为Rocky Linux系统设计，提供了从环境检查到自动化部署的完整解决方案。

## 📁 文件结构

```
jenkins-deploy/
├── README.md                    # 详细部署文档（英文）
├── 项目说明.md                  # 项目概述（中文）
├── .env.example                 # 环境变量配置模板
├── quick-start.sh               # 一键快速启动脚本 ⭐
├── deploy-jenkins.sh            # 自动化部署脚本
├── jenkins-manager.sh           # Jenkins管理脚本
├── docker-compose.yml           # 完整配置（包含代理和Nginx）
├── docker-compose-simple.yml    # 简化配置（推荐新手）
└── nginx/
    └── nginx.conf               # Nginx反向代理配置
```

## 🚀 快速使用

### 方式一：一键启动（推荐）
```bash
# 进入部署目录
cd jenkins-deploy

# 给脚本添加执行权限
chmod +x quick-start.sh

# 一键启动（交互式选择模式）
./quick-start.sh

# 或者直接使用简化模式
./quick-start.sh --simple

# 或者直接使用完整模式
./quick-start.sh --full
```

### 方式二：手动部署
```bash
# 进入部署目录
cd jenkins-deploy

# 给脚本添加执行权限
chmod +x deploy-jenkins.sh jenkins-manager.sh

# 简化部署
./deploy-jenkins.sh simple

# 完整部署
./deploy-jenkins.sh
```

## 🔧 管理命令

```bash
# 查看服务状态
./jenkins-manager.sh status

# 获取初始密码
./jenkins-manager.sh password

# 查看日志
./jenkins-manager.sh logs

# 重启服务
./jenkins-manager.sh restart

# 停止服务
./jenkins-manager.sh stop

# 备份数据
./jenkins-manager.sh backup

# 查看所有命令
./jenkins-manager.sh help
```

## 📋 部署模式

### 1. 简化模式（推荐新手）
- ✅ Jenkins核心功能
- ✅ Docker支持
- ✅ 数据持久化
- ✅ 健康检查
- ❌ Jenkins代理
- ❌ Nginx反向代理

**适用场景：**
- 个人学习和测试
- 小型项目开发
- 快速体验Jenkins

### 2. 完整模式（推荐生产）
- ✅ Jenkins核心功能
- ✅ Docker支持
- ✅ 数据持久化
- ✅ 健康检查
- ✅ Jenkins代理节点
- ✅ Nginx反向代理
- ✅ SSL支持
- ✅ 负载均衡

**适用场景：**
- 生产环境部署
- 大型项目开发
- 需要高可用性

## 🔍 核心特性

### 🛡️ 安全性
- 数据持久化存储
- 用户权限管理
- 防火墙自动配置
- SSL证书支持

### 🚀 性能优化
- JVM参数调优
- 资源限制配置
- 健康检查机制
- 自动重启策略

### 📊 监控管理
- 实时日志查看
- 资源使用监控
- 自动备份功能
- 一键恢复机制

### 🔧 易于维护
- 脚本化管理
- 模块化配置
- 详细文档说明
- 故障排除指南

## 📈 系统要求

| 配置级别 | CPU | 内存 | 磁盘空间 | 适用场景 |
|---------|-----|------|----------|----------|
| 最小配置 | 2核 | 2GB | 10GB | 学习测试 |
| 推荐配置 | 4核 | 4GB | 50GB | 中小项目 |
| 生产配置 | 8核 | 8GB | 100GB | 大型项目 |

## 🎯 使用场景

### 1. 持续集成/持续部署（CI/CD）
- 自动化代码构建
- 自动化测试执行
- 自动化部署流程
- 多环境管理

### 2. 项目管理
- 多分支构建
- 代码质量检查
- 构建报告生成
- 团队协作

### 3. DevOps实践
- 基础设施即代码
- 容器化部署
- 微服务架构
- 监控告警

## 🔗 相关链接

- [Jenkins官方文档](https://www.jenkins.io/doc/)
- [Docker官方文档](https://docs.docker.com/)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [Rocky Linux官网](https://rockylinux.org/)

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看日志**：`./jenkins-manager.sh logs`
2. **检查状态**：`./jenkins-manager.sh status`
3. **查看文档**：`cat README.md`
4. **系统诊断**：`./jenkins-manager.sh info`

## 📝 更新日志

- **v1.0** - 初始版本，支持基本部署
- **v1.1** - 添加快速启动脚本
- **v1.2** - 优化配置文件和文档

## 🤝 贡献

欢迎提交问题和改进建议！

---

**注意：** 请确保在Rocky Linux系统上运行，其他Linux发行版可能需要调整部分命令。
