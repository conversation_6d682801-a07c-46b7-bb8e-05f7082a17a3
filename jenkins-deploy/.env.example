# Jenkins Docker Compose 环境变量配置文件
# 复制此文件为 .env 并根据您的环境修改配置

# ===========================================
# Jenkins 基本配置
# ===========================================

# Jenkins Web UI 端口
JENKINS_HTTP_PORT=8080

# Jenkins 代理连接端口
JENKINS_AGENT_PORT=50000

# Jenkins 时区设置
TZ=Asia/Shanghai

# ===========================================
# Java 虚拟机配置
# ===========================================

# JVM 堆内存设置（根据服务器配置调整）
# 最小配置：-Xmx1024m -Xms512m
# 推荐配置：-Xmx2048m -Xms1024m
# 高配置：-Xmx4096m -Xms2048m
JAVA_OPTS=-Xmx2048m -Xms1024m -XX:+UseG1GC

# Jenkins 启动选项
JENKINS_OPTS=--httpPort=8080

# ===========================================
# 数据持久化配置
# ===========================================

# Jenkins 主目录路径（宿主机路径）
JENKINS_HOME_PATH=/opt/jenkins/jenkins_home

# Jenkins 备份目录路径
JENKINS_BACKUP_PATH=./jenkins-backup

# ===========================================
# 网络配置
# ===========================================

# Docker 网络子网
DOCKER_SUBNET=**********/16

# ===========================================
# Nginx 配置（如果使用反向代理）
# ===========================================

# HTTP 端口
NGINX_HTTP_PORT=80

# HTTPS 端口
NGINX_HTTPS_PORT=443

# 域名配置
DOMAIN_NAME=your-domain.com

# ===========================================
# 安全配置
# ===========================================

# 是否跳过初始设置向导（生产环境建议设为false）
SKIP_SETUP_WIZARD=false

# Jenkins 代理密钥（需要在Jenkins启动后获取）
# JENKINS_AGENT_SECRET=your-agent-secret-here

# ===========================================
# 资源限制配置
# ===========================================

# 内存限制
MEMORY_LIMIT=3G

# CPU 限制
CPU_LIMIT=2.0

# 内存预留
MEMORY_RESERVATION=1G

# CPU 预留
CPU_RESERVATION=1.0

# ===========================================
# 日志配置
# ===========================================

# 日志级别
LOG_LEVEL=INFO

# 日志文件大小限制
LOG_MAX_SIZE=10m

# 日志文件数量限制
LOG_MAX_FILES=3
